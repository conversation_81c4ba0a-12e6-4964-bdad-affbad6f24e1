// models/Student.ts

import mongoose, { Schema, Document, model } from "mongoose";
import { IProfile, ProfileSchema } from "./profile";

const allowedRoles = ["student"] as const;
type RoleType = typeof allowedRoles[number]; // "student"

export interface IStudent extends Document, IProfile {
  role: RoleType;
  learningReasons: string[];
  skillsToImprove: string[];
  needIndustryKnowledge: boolean;
  preferredLessonDuration: string;
  stripeCustomerId?: string; // Made optional as it might not exist initially
  stripeAccountId?: string; // Made optional as it might not exist initially
  isActive: boolean;
  hasUsedFreeTrial: boolean;
}

const StudentSchema = new Schema(
  {
    learningReasons: {
      type: [String],
      default: [],
    },
    skillsToImprove: {
      type: [String],
      default: [],
    },
    needIndustryKnowledge: {
      type: Boolean,
      default: false,
    },
    preferredLessonDuration: {
      type: String,
    },
    stripeCustomerId: {
      type: String,
    },
    stripeAccountId: {
      type: String,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    hasUsedFreeTrial: {
      type: Boolean,
      default: false,
    },
    role: {
      type: String,
      enum: allowedRoles,
      default: "student",
    },
  },
  {
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
    timestamps: true,
  }
);

// Add profile sub-schema
StudentSchema.add(ProfileSchema);

// Virtuals
StudentSchema.virtual("user", {
  ref: "User",
  localField: "userId",
  foreignField: "_id",
  justOne: true,
});

StudentSchema.virtual("activeSubscription", {
  ref: "Subscription",
  localField: "_id",
  foreignField: "studentId",
  justOne: true,
  match: { status: "active" },
});

StudentSchema.virtual("subscriptions", {
  ref: "Subscription",
  localField: "_id",
  foreignField: "studentId",
});

StudentSchema.virtual("lessons", {
  ref: "Lesson",
  localField: "_id",
  foreignField: "studentId",
});

const Student = model<IStudent>("Student", StudentSchema); // Explicitly type the model
export default Student;
