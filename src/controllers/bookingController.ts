import { Request, Response } from 'express';
import { Booking } from '../models/Booking';
import { Event } from '../models/Event';

// <PERSON><PERSON> creates a booking request for an event/session
export const createBooking = async (req: Request, res: Response): Promise<any> => {
  try {
    if (req.user.role !== 'learner') {
      return res.status(403).json({ message: 'Only learners can create bookings' });
    }

    const { eventId } = req.body;

    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: 'Event not found' });

    // Check if booking already exists for this learner and event
    const existingBooking = await Booking.findOne({ eventId, learnerId: req.user.id });
    if (existingBooking) {
      return res.status(409).json({ message: 'You already booked this session' });
    }

    const booking = new Booking({
      eventId,
      learnerId: req.user.id,
      status: 'pending',
    });

    await booking.save();
    res.status(201).json(booking);
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};

// <PERSON><PERSON> gets bookings for their events
export const getBookingsForTutor = async (req: Request, res: Response): Promise<any> => {
  try {
    if (req.user.role !== 'tutor') {
      return res.status(403).json({ message: 'Only tutors can view bookings' });
    }

    // Get all tutor's events
    const events = await Event.find({ createdBy: req.user.id });
    const eventIds = events.map(e => e._id);

    // Find bookings for those events
    const bookings = await Booking.find({ eventId: { $in: eventIds } }).populate('learnerId', 'name email');
    res.json(bookings);
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};

// Update booking status (only tutor)
export const updateBookingStatus = async (req: Request, res: Response): Promise<any> => {
  try {
    if (req.user.role !== 'tutor') {
      return res.status(403).json({ message: 'Only tutors can update booking status' });
    }

    const bookingId = req.params.id;
    const { status } = req.body; // should be one of 'pending' | 'confirmed' | 'cancelled'

    const booking = await Booking.findById(bookingId);
    if (!booking) return res.status(404).json({ message: 'Booking not found' });

    // Verify tutor owns the event linked to booking
    const event = await Event.findById(booking.eventId);
    if (!event) return res.status(404).json({ message: 'Event not found' });
    // if (event.createdBy.toString() !== req.user.id) {
    //   return res.status(403).json({ message: 'Not authorized to update this booking' });
    // }

    if (!['pending', 'confirmed', 'cancelled'].includes(status)) {
      return res.status(400).json({ message: 'Invalid status' });
    }

    booking.status = status;
    booking.updatedAt = new Date();

    await booking.save();
    res.json(booking);
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};
