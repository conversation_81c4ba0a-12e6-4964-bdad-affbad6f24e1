import { Response } from 'express';
import { Types } from 'mongoose';
import { Calendar } from '../models/calender';
import { Event } from '../models/Event';
import { Booking } from '../models/Booking';
import Student from '../models/student';
import Tutor from '../models/tutor';
import Subscription from '../models/subscription.model';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';
import { checkBookingEligibility } from '../middlewares/subscriptionValidation';

/**
 * Get available time slots for a specific tutor
 * This shows all available slots that students can book
 */
export const getAvailableTimeSlots = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const { tutorId } = req.params;
    const { 
      startDate, 
      endDate, 
      duration = 60, // Default 60 minutes
      timezone = 'UTC' 
    } = req.query;

    if (!tutorId || !Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, 'Invalid tutor ID format', 400);
      return;
    }

    // Validate date range
    const start = startDate ? new Date(startDate as string) : new Date();
    const end = endDate ? new Date(endDate as string) : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days from now

    if (start >= end) {
      createErrorResponse(res, 'Start date must be before end date', 400);
      return;
    }

    // Get tutor and their calendars
    const tutor = await Tutor.findById(tutorId);
    if (!tutor) {
      createErrorResponse(res, 'Tutor not found', 404);
      return;
    }

    if (tutor.approvalStatus !== 'approved' || !tutor.isActive) {
      createErrorResponse(res, 'Tutor is not available for booking', 400);
      return;
    }

    // Get tutor's active calendars
    const calendars = await Calendar.find({
      tutorId: new Types.ObjectId(tutorId),
      isActive: true,
      isShared: true
    });

    if (calendars.length === 0) {
      res.json({
        success: true,
        data: {
          tutor: {
            _id: tutor._id,
            firstname: tutor.firstname,
            lastname: tutor.lastname,
            basePrice: tutor.basePrice,
            rating: tutor.rating
          },
          availableSlots: [],
          message: 'Tutor has no available calendars'
        }
      });
      return;
    }

    // Get all available events in the date range
    const availableEvents = await Event.find({
      tutorId: new Types.ObjectId(tutorId),
      startDateTime: { $gte: start, $lte: end },
      status: 'available',
      'bookingInfo.currentBookings': { $lt: { $ifNull: ['$bookingInfo.maxStudents', 1] } }
    }).populate('calendarId', 'name color bookingSettings')
      .sort({ startDateTime: 1 });

    // Check booking eligibility if user is a student
    let bookingEligibility = null;
    if (req.user && req.user.role === 'student') {
      bookingEligibility = await checkBookingEligibility(
        req.user._id as Types.ObjectId,
        new Types.ObjectId(tutorId)
      );
    }

    // Format the response
    const availableSlots = availableEvents.map(event => ({
      eventId: event._id,
      calendarId: event.calendarId._id,
      calendarName: (event.calendarId as any).name,
      title: event.title,
      startDateTime: event.startDateTime,
      endDateTime: event.endDateTime,
      duration: Math.floor((event.endDateTime.getTime() - event.startDateTime.getTime()) / (1000 * 60)),
      location: event.location,
      price: event.bookingInfo?.price || tutor.basePrice,
      maxStudents: event.bookingInfo?.maxStudents || 1,
      currentBookings: event.bookingInfo?.currentBookings || 0,
      requiresApproval: event.bookingInfo?.requiresApproval || false,
      canBook: bookingEligibility?.canBook || false
    }));

    res.json({
      success: true,
      data: {
        tutor: {
          _id: tutor._id,
          firstname: tutor.firstname,
          lastname: tutor.lastname,
          basePrice: tutor.basePrice,
          rating: tutor.rating,
          totalLessons: tutor.totalLessons
        },
        availableSlots,
        bookingEligibility,
        dateRange: { startDate: start, endDate: end },
        timezone
      }
    });

  } catch (error) {
    console.error('Error fetching available time slots:', error);
    createErrorResponse(res, 'Failed to fetch available time slots', 500);
  }
};

/**
 * Schedule a lesson - creates a booking for an available time slot
 */
export const scheduleLesson = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can schedule lessons', 403);
      return;
    }

    const { 
      eventId, 
      bookingNotes, 
      specialRequests,
      preferredCommunicationMethod = 'platform' 
    } = req.body;

    if (!eventId || !Types.ObjectId.isValid(eventId)) {
      createErrorResponse(res, 'Invalid event ID format', 400);
      return;
    }

    // Get the event with populated data
    const event = await Event.findById(eventId)
      .populate('calendarId')
      .populate('tutorId', 'firstname lastname email basePrice');

    if (!event) {
      createErrorResponse(res, 'Time slot not found', 404);
      return;
    }

    // Verify event is available
    const now = new Date();
    const isAvailable = event.status === 'available' && 
                       event.startDateTime > now && 
                       (event.bookingInfo?.currentBookings || 0) < (event.bookingInfo?.maxStudents || 1);

    if (!isAvailable) {
      createErrorResponse(res, 'This time slot is no longer available', 400);
      return;
    }

    // Check if student already has a booking for this event
    const existingBooking = await Booking.findOne({
      eventId: event._id,
      studentId: req.user._id,
      status: { $in: ['pending', 'confirmed'] }
    });

    if (existingBooking) {
      createErrorResponse(res, 'You already have a booking for this time slot', 409);
      return;
    }

    // Validate subscription or trial eligibility (handled by middleware)
    if (!req.subscription && !req.isTrialBooking) {
      createErrorResponse(res, 'No valid subscription or trial eligibility found', 403);
      return;
    }

    // For trial bookings, ensure it's 1 hour or less
    if (req.isTrialBooking) {
      const durationMinutes = Math.floor((event.endDateTime.getTime() - event.startDateTime.getTime()) / (1000 * 60));
      if (durationMinutes > 60) {
        createErrorResponse(res, 'Free trial is limited to 1-hour sessions only', 400);
        return;
      }
    }

    // Check for scheduling conflicts (student can't book overlapping sessions)
    const conflictingBookings = await Booking.find({
      studentId: req.user._id,
      status: { $in: ['pending', 'confirmed'] }
    }).populate('eventId', 'startDateTime endDateTime');

    const hasConflict = conflictingBookings.some(booking => {
      const bookingEvent = booking.eventId as any;
      return (
        (event.startDateTime >= bookingEvent.startDateTime && event.startDateTime < bookingEvent.endDateTime) ||
        (event.endDateTime > bookingEvent.startDateTime && event.endDateTime <= bookingEvent.endDateTime) ||
        (event.startDateTime <= bookingEvent.startDateTime && event.endDateTime >= bookingEvent.endDateTime)
      );
    });

    if (hasConflict) {
      createErrorResponse(res, 'You have a conflicting booking at this time', 409);
      return;
    }

    // Create the booking
    const bookingData: any = {
      eventId: event._id,
      studentId: req.user._id,
      tutorId: event.tutorId,
      status: event.bookingInfo?.requiresApproval ? 'pending' : 'confirmed',
      bookingNotes,
      metadata: {
        specialRequests,
        preferredCommunicationMethod,
        isTrialBooking: req.isTrialBooking || false,
        price: event.bookingInfo?.price || (event.tutorId as any).basePrice
      }
    };

    // Add subscription ID if this is a subscription-based booking
    if (req.subscription) {
      bookingData.subscriptionId = req.subscription._id;
    }

    const booking = new Booking(bookingData);
    await booking.save();

    // Update event booking count
    await Event.findByIdAndUpdate(eventId, {
      $inc: { 'bookingInfo.currentBookings': 1 },
      status: (event.bookingInfo?.currentBookings || 0) + 1 >= (event.bookingInfo?.maxStudents || 1) ? 'booked' : 'available'
    });

    // If this is a trial booking, mark student as having used their trial
    if (req.isTrialBooking) {
      await Student.findByIdAndUpdate(req.user._id, {
        hasUsedFreeTrial: true
      });
    }

    // If subscription booking, decrement remaining lessons
    if (req.subscription) {
      await Subscription.findByIdAndUpdate(req.subscription._id, {
        $inc: { remainingLessons: -1 }
      });
    }

    // Get the populated booking for response
    const populatedBooking = await Booking.findById(booking._id)
      .populate('eventId', 'title startDateTime endDateTime location')
      .populate('tutorId', 'firstname lastname email avatar')
      .populate('studentId', 'firstname lastname email')
      .populate('subscriptionId', 'planType status remainingLessons');

    res.status(201).json({
      success: true,
      message: req.isTrialBooking ? 
        'Trial lesson scheduled successfully! Enjoy your free session.' : 
        'Lesson scheduled successfully',
      data: {
        booking: populatedBooking,
        isTrialBooking: req.isTrialBooking || false,
        requiresApproval: event.bookingInfo?.requiresApproval || false,
        remainingLessons: req.subscription?.remainingLessons || 0
      }
    });

  } catch (error) {
    console.error('Error scheduling lesson:', error);
    createErrorResponse(res, 'Failed to schedule lesson', 500);
  }
};

/**
 * Get student's scheduled lessons
 */
export const getMyScheduledLessons = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can view their scheduled lessons', 403);
      return;
    }

    const {
      status,
      page = 1,
      limit = 20,
      upcoming = false,
      startDate,
      endDate
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);

    // Build query
    const query: any = { studentId: req.user._id };

    if (status && ['pending', 'confirmed', 'cancelled', 'completed', 'no_show'].includes(status as string)) {
      query.status = status;
    }

    // Build event query for date filtering
    let eventQuery: any = {};
    if (upcoming === 'true') {
      eventQuery.startDateTime = { $gte: new Date() };
    } else if (startDate || endDate) {
      eventQuery.startDateTime = {};
      if (startDate) eventQuery.startDateTime.$gte = new Date(startDate as string);
      if (endDate) eventQuery.startDateTime.$lte = new Date(endDate as string);
    }

    // Get bookings with populated event data
    const bookings = await Booking.find(query)
      .populate({
        path: 'eventId',
        select: 'title startDateTime endDateTime location',
        match: eventQuery
      })
      .populate('tutorId', 'firstname lastname email avatar basePrice rating')
      .populate('subscriptionId', 'planType status remainingLessons')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(limit));

    // Filter out bookings where event was null (due to populate match)
    const filteredBookings = bookings.filter(booking => booking.eventId);

    // Get total count
    const total = await Booking.countDocuments(query);

    // Format response with additional info
    const formattedBookings = filteredBookings.map(booking => {
      const event = booking.eventId as any;
      const tutor = booking.tutorId as any;

      return {
        ...booking.toObject(),
        lessonInfo: {
          title: event.title,
          startDateTime: event.startDateTime,
          endDateTime: event.endDateTime,
          duration: Math.floor((event.endDateTime.getTime() - event.startDateTime.getTime()) / (1000 * 60)),
          location: event.location,
          isUpcoming: event.startDateTime > new Date(),
          timeUntilLesson: event.startDateTime > new Date() ?
            Math.floor((event.startDateTime.getTime() - new Date().getTime()) / (1000 * 60 * 60)) : null
        },
        tutorInfo: {
          name: `${tutor.firstname} ${tutor.lastname}`,
          email: tutor.email,
          avatar: tutor.avatar,
          rating: tutor.rating
        },
        isTrialLesson: !booking.subscriptionId
      };
    });

    res.json({
      success: true,
      data: formattedBookings,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: upcoming === 'true' ? filteredBookings.length : total,
        pages: Math.ceil((upcoming === 'true' ? filteredBookings.length : total) / Number(limit))
      }
    });

  } catch (error) {
    console.error('Error fetching scheduled lessons:', error);
    createErrorResponse(res, 'Failed to fetch scheduled lessons', 500);
  }
};

/**
 * Reschedule a lesson to a different time slot
 */
export const rescheduleLesson = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can reschedule their lessons', 403);
      return;
    }

    const { bookingId } = req.params;
    const { newEventId, reason } = req.body;

    if (!bookingId || !Types.ObjectId.isValid(bookingId)) {
      createErrorResponse(res, 'Invalid booking ID format', 400);
      return;
    }

    if (!newEventId || !Types.ObjectId.isValid(newEventId)) {
      createErrorResponse(res, 'Invalid new event ID format', 400);
      return;
    }

    // Get the current booking
    const currentBooking = await Booking.findById(bookingId)
      .populate('eventId', 'startDateTime endDateTime tutorId')
      .populate('tutorId', 'firstname lastname email');

    if (!currentBooking) {
      createErrorResponse(res, 'Booking not found', 404);
      return;
    }

    // Verify ownership
    if (currentBooking.studentId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'Not authorized to reschedule this booking', 403);
      return;
    }

    // Check if booking can be rescheduled
    if (!['pending', 'confirmed'].includes(currentBooking.status)) {
      createErrorResponse(res, 'This booking cannot be rescheduled', 400);
      return;
    }

    // Check rescheduling policy (e.g., minimum notice period)
    const currentEvent = currentBooking.eventId as any;
    const hoursUntilLesson = (currentEvent.startDateTime.getTime() - new Date().getTime()) / (1000 * 60 * 60);

    if (hoursUntilLesson < 24) {
      createErrorResponse(res, 'Lessons must be rescheduled at least 24 hours in advance', 400);
      return;
    }

    // Get the new event
    const newEvent = await Event.findById(newEventId)
      .populate('tutorId', 'firstname lastname email');

    if (!newEvent) {
      createErrorResponse(res, 'New time slot not found', 404);
      return;
    }

    // Verify it's the same tutor
    if (newEvent.tutorId._id.toString() !== currentEvent.tutorId.toString()) {
      createErrorResponse(res, 'Can only reschedule with the same tutor', 400);
      return;
    }

    // Verify new event is available
    const isNewEventAvailable = newEvent.status === 'available' &&
                               newEvent.startDateTime > new Date() &&
                               (newEvent.bookingInfo?.currentBookings || 0) < (newEvent.bookingInfo?.maxStudents || 1);

    if (!isNewEventAvailable) {
      createErrorResponse(res, 'New time slot is not available', 400);
      return;
    }

    // Check for conflicts with student's other bookings
    const conflictingBookings = await Booking.find({
      studentId: req.user._id,
      _id: { $ne: bookingId },
      status: { $in: ['pending', 'confirmed'] }
    }).populate('eventId', 'startDateTime endDateTime');

    const hasConflict = conflictingBookings.some(booking => {
      const bookingEvent = booking.eventId as any;
      return (
        (newEvent.startDateTime >= bookingEvent.startDateTime && newEvent.startDateTime < bookingEvent.endDateTime) ||
        (newEvent.endDateTime > bookingEvent.startDateTime && newEvent.endDateTime <= bookingEvent.endDateTime) ||
        (newEvent.startDateTime <= bookingEvent.startDateTime && newEvent.endDateTime >= bookingEvent.endDateTime)
      );
    });

    if (hasConflict) {
      createErrorResponse(res, 'You have a conflicting booking at the new time', 409);
      return;
    }

    // Update the booking
    currentBooking.eventId = newEvent._id;
    currentBooking.status = newEvent.bookingInfo?.requiresApproval ? 'pending' : 'confirmed';
    if (reason) {
      currentBooking.bookingNotes = (currentBooking.bookingNotes || '') + `\n\nRescheduled: ${reason}`;
    }
    await currentBooking.save();

    // Update event booking counts
    await Promise.all([
      // Decrease count for old event
      Event.findByIdAndUpdate(currentEvent._id, {
        $inc: { 'bookingInfo.currentBookings': -1 },
        status: 'available'
      }),
      // Increase count for new event
      Event.findByIdAndUpdate(newEvent._id, {
        $inc: { 'bookingInfo.currentBookings': 1 },
        status: (newEvent.bookingInfo?.currentBookings || 0) + 1 >= (newEvent.bookingInfo?.maxStudents || 1) ? 'booked' : 'available'
      })
    ]);

    // Get updated booking for response
    const updatedBooking = await Booking.findById(bookingId)
      .populate('eventId', 'title startDateTime endDateTime location')
      .populate('tutorId', 'firstname lastname email')
      .populate('subscriptionId', 'planType status');

    res.json({
      success: true,
      message: 'Lesson rescheduled successfully',
      data: updatedBooking
    });

  } catch (error) {
    console.error('Error rescheduling lesson:', error);
    createErrorResponse(res, 'Failed to reschedule lesson', 500);
  }
};

/**
 * Get tutor's schedule (for tutors to view their bookings)
 */
export const getTutorSchedule = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can view their schedule', 403);
      return;
    }

    const {
      status,
      page = 1,
      limit = 20,
      startDate,
      endDate,
      calendarId
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);

    // Build query
    const query: any = { tutorId: req.user._id };

    if (status && ['pending', 'confirmed', 'cancelled', 'completed', 'no_show'].includes(status as string)) {
      query.status = status;
    }

    // Build event query for filtering
    let eventQuery: any = { tutorId: req.user._id };
    if (startDate || endDate) {
      eventQuery.startDateTime = {};
      if (startDate) eventQuery.startDateTime.$gte = new Date(startDate as string);
      if (endDate) eventQuery.startDateTime.$lte = new Date(endDate as string);
    }
    if (calendarId && Types.ObjectId.isValid(calendarId as string)) {
      eventQuery.calendarId = new Types.ObjectId(calendarId as string);
    }

    // Get events in date range first
    const events = await Event.find(eventQuery).select('_id');
    query.eventId = { $in: events.map(e => e._id) };

    const [bookings, total] = await Promise.all([
      Booking.find(query)
        .populate('studentId', 'firstname lastname email avatar')
        .populate({
          path: 'eventId',
          select: 'title startDateTime endDateTime location calendarId',
          populate: {
            path: 'calendarId',
            select: 'name color'
          }
        })
        .populate('subscriptionId', 'planType status')
        .sort({ 'eventId.startDateTime': 1 })
        .skip(skip)
        .limit(Number(limit)),
      Booking.countDocuments(query)
    ]);

    // Format response
    const formattedBookings = bookings.map(booking => {
      const event = booking.eventId as any;
      const student = booking.studentId as any;

      return {
        ...booking.toObject(),
        lessonInfo: {
          title: event.title,
          startDateTime: event.startDateTime,
          endDateTime: event.endDateTime,
          duration: Math.floor((event.endDateTime.getTime() - event.startDateTime.getTime()) / (1000 * 60)),
          location: event.location,
          calendar: event.calendarId
        },
        studentInfo: {
          name: `${student.firstname} ${student.lastname}`,
          email: student.email,
          avatar: student.avatar
        },
        isTrialLesson: !booking.subscriptionId
      };
    });

    res.json({
      success: true,
      data: formattedBookings,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });

  } catch (error) {
    console.error('Error fetching tutor schedule:', error);
    createErrorResponse(res, 'Failed to fetch schedule', 500);
  }
};

/**
 * Get booking details by ID
 */
export const getBookingDetails = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      createErrorResponse(res, 'Authentication required', 401);
      return;
    }

    const { bookingId } = req.params;

    if (!bookingId || !Types.ObjectId.isValid(bookingId)) {
      createErrorResponse(res, 'Invalid booking ID format', 400);
      return;
    }

    const booking = await Booking.findById(bookingId)
      .populate('studentId', 'firstname lastname email avatar')
      .populate('tutorId', 'firstname lastname email avatar basePrice rating')
      .populate({
        path: 'eventId',
        select: 'title startDateTime endDateTime location calendarId',
        populate: {
          path: 'calendarId',
          select: 'name color bookingSettings'
        }
      })
      .populate('subscriptionId', 'planType status remainingLessons monthlyPrice');

    if (!booking) {
      createErrorResponse(res, 'Booking not found', 404);
      return;
    }

    // Check authorization
    const isStudent = req.user.role === 'student' && booking.studentId._id.toString() === (req.user._id as Types.ObjectId).toString();
    const isTutor = req.user.role === 'tutor' && booking.tutorId._id.toString() === (req.user._id as Types.ObjectId).toString();

    if (!isStudent && !isTutor) {
      createErrorResponse(res, 'Not authorized to view this booking', 403);
      return;
    }

    const event = booking.eventId as any;
    const student = booking.studentId as any;
    const tutor = booking.tutorId as any;

    // Format detailed response
    const bookingDetails = {
      ...booking.toObject(),
      lessonInfo: {
        title: event.title,
        startDateTime: event.startDateTime,
        endDateTime: event.endDateTime,
        duration: Math.floor((event.endDateTime.getTime() - event.startDateTime.getTime()) / (1000 * 60)),
        location: event.location,
        calendar: event.calendarId,
        isUpcoming: event.startDateTime > new Date(),
        timeUntilLesson: event.startDateTime > new Date() ?
          Math.floor((event.startDateTime.getTime() - new Date().getTime()) / (1000 * 60 * 60)) : null,
        canBeCancelled: ['pending', 'confirmed'].includes(booking.status) &&
                       (event.startDateTime.getTime() - new Date().getTime()) > (24 * 60 * 60 * 1000), // 24 hours notice
        canBeRescheduled: ['pending', 'confirmed'].includes(booking.status) &&
                         (event.startDateTime.getTime() - new Date().getTime()) > (24 * 60 * 60 * 1000)
      },
      studentInfo: {
        name: `${student.firstname} ${student.lastname}`,
        email: student.email,
        avatar: student.avatar
      },
      tutorInfo: {
        name: `${tutor.firstname} ${tutor.lastname}`,
        email: tutor.email,
        avatar: tutor.avatar,
        basePrice: tutor.basePrice,
        rating: tutor.rating
      },
      isTrialLesson: !booking.subscriptionId,
      subscriptionInfo: booking.subscriptionId || null
    };

    res.json({
      success: true,
      data: bookingDetails
    });

  } catch (error) {
    console.error('Error fetching booking details:', error);
    createErrorResponse(res, 'Failed to fetch booking details', 500);
  }
};

/**
 * Get student's scheduled lessons
 */
export const getMyScheduledLessons = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can view their scheduled lessons', 403);
      return;
    }

    const {
      status,
      page = 1,
      limit = 20,
      upcoming = false,
      startDate,
      endDate
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);

    // Build query
    const query: any = { studentId: req.user._id };

    if (status && ['pending', 'confirmed', 'cancelled', 'completed', 'no_show'].includes(status as string)) {
      query.status = status;
    }

    // Get bookings with event date filtering
    let eventDateFilter: any = {};
    if (upcoming === 'true') {
      eventDateFilter.startDateTime = { $gte: new Date() };
    } else if (startDate || endDate) {
      if (startDate) eventDateFilter.startDateTime = { $gte: new Date(startDate as string) };
      if (endDate) {
        eventDateFilter.startDateTime = eventDateFilter.startDateTime || {};
        eventDateFilter.startDateTime.$lte = new Date(endDate as string);
      }
    }

    const [bookings, total] = await Promise.all([
      Booking.find(query)
        .populate({
          path: 'eventId',
          select: 'title startDateTime endDateTime location',
          match: Object.keys(eventDateFilter).length > 0 ? eventDateFilter : {}
        })
        .populate('tutorId', 'firstname lastname email avatar basePrice rating')
        .populate('subscriptionId', 'planType status remainingLessons')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Number(limit)),
      Booking.countDocuments(query)
    ]);

    // Filter out bookings where event was null (due to populate match)
    const filteredBookings = bookings.filter(booking => booking.eventId);

    // Add additional info for each booking
    const enrichedBookings = filteredBookings.map(booking => {
      const event = booking.eventId as any;
      const now = new Date();

      return {
        ...booking.toObject(),
        lessonInfo: {
          duration: Math.floor((event.endDateTime.getTime() - event.startDateTime.getTime()) / (1000 * 60)),
          isUpcoming: event.startDateTime > now,
          isPast: event.endDateTime < now,
          isToday: event.startDateTime.toDateString() === now.toDateString(),
          timeUntilLesson: event.startDateTime > now ?
            Math.floor((event.startDateTime.getTime() - now.getTime()) / (1000 * 60 * 60)) : null
        }
      };
    });

    res.json({
      success: true,
      data: enrichedBookings,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total: upcoming === 'true' ? filteredBookings.length : total,
        pages: Math.ceil((upcoming === 'true' ? filteredBookings.length : total) / Number(limit))
      }
    });

  } catch (error) {
    console.error('Error fetching scheduled lessons:', error);
    createErrorResponse(res, 'Failed to fetch scheduled lessons', 500);
  }
};

/**
 * Reschedule a lesson to a different time slot
 */
export const rescheduleLesson = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'student') {
      createErrorResponse(res, 'Only students can reschedule their lessons', 403);
      return;
    }

    const { bookingId } = req.params;
    const { newEventId, reason } = req.body;

    if (!bookingId || !Types.ObjectId.isValid(bookingId)) {
      createErrorResponse(res, 'Invalid booking ID format', 400);
      return;
    }

    if (!newEventId || !Types.ObjectId.isValid(newEventId)) {
      createErrorResponse(res, 'Invalid new event ID format', 400);
      return;
    }

    // Get the current booking
    const currentBooking = await Booking.findById(bookingId)
      .populate('eventId', 'startDateTime endDateTime tutorId')
      .populate('tutorId', 'firstname lastname email');

    if (!currentBooking) {
      createErrorResponse(res, 'Booking not found', 404);
      return;
    }

    // Verify student owns this booking
    if (currentBooking.studentId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'Not authorized to reschedule this booking', 403);
      return;
    }

    // Check if booking can be rescheduled
    if (!['pending', 'confirmed'].includes(currentBooking.status)) {
      createErrorResponse(res, 'This booking cannot be rescheduled', 400);
      return;
    }

    // Check rescheduling policy (e.g., minimum notice period)
    const currentEvent = currentBooking.eventId as any;
    const hoursUntilLesson = (currentEvent.startDateTime.getTime() - new Date().getTime()) / (1000 * 60 * 60);

    if (hoursUntilLesson < 24) {
      createErrorResponse(res, 'Lessons must be rescheduled at least 24 hours in advance', 400);
      return;
    }

    // Get the new event
    const newEvent = await Event.findById(newEventId)
      .populate('tutorId', 'firstname lastname email');

    if (!newEvent) {
      createErrorResponse(res, 'New time slot not found', 404);
      return;
    }

    // Verify new event is with the same tutor
    if (newEvent.tutorId._id.toString() !== currentEvent.tutorId.toString()) {
      createErrorResponse(res, 'New time slot must be with the same tutor', 400);
      return;
    }

    // Verify new event is available
    const now = new Date();
    const isNewEventAvailable = newEvent.status === 'available' &&
                               newEvent.startDateTime > now &&
                               (newEvent.bookingInfo?.currentBookings || 0) < (newEvent.bookingInfo?.maxStudents || 1);

    if (!isNewEventAvailable) {
      createErrorResponse(res, 'New time slot is not available', 400);
      return;
    }

    // Check for conflicts with student's other bookings
    const conflictingBookings = await Booking.find({
      studentId: req.user._id,
      _id: { $ne: bookingId },
      status: { $in: ['pending', 'confirmed'] }
    }).populate('eventId', 'startDateTime endDateTime');

    const hasConflict = conflictingBookings.some(booking => {
      const bookingEvent = booking.eventId as any;
      return (
        (newEvent.startDateTime >= bookingEvent.startDateTime && newEvent.startDateTime < bookingEvent.endDateTime) ||
        (newEvent.endDateTime > bookingEvent.startDateTime && newEvent.endDateTime <= bookingEvent.endDateTime) ||
        (newEvent.startDateTime <= bookingEvent.startDateTime && newEvent.endDateTime >= bookingEvent.endDateTime)
      );
    });

    if (hasConflict) {
      createErrorResponse(res, 'You have a conflicting booking at the new time', 409);
      return;
    }

    // Update the booking
    currentBooking.eventId = newEvent._id;
    currentBooking.status = newEvent.bookingInfo?.requiresApproval ? 'pending' : 'confirmed';
    if (reason) {
      currentBooking.bookingNotes = (currentBooking.bookingNotes || '') + `\n\nRescheduled: ${reason}`;
    }
    await currentBooking.save();

    // Update event booking counts
    await Promise.all([
      // Decrease count for old event
      Event.findByIdAndUpdate(currentEvent._id, {
        $inc: { 'bookingInfo.currentBookings': -1 },
        status: 'available'
      }),
      // Increase count for new event
      Event.findByIdAndUpdate(newEvent._id, {
        $inc: { 'bookingInfo.currentBookings': 1 },
        status: (newEvent.bookingInfo?.currentBookings || 0) + 1 >= (newEvent.bookingInfo?.maxStudents || 1) ? 'booked' : 'available'
      })
    ]);

    // Get updated booking for response
    const updatedBooking = await Booking.findById(bookingId)
      .populate('eventId', 'title startDateTime endDateTime location')
      .populate('tutorId', 'firstname lastname email avatar')
      .populate('subscriptionId', 'planType status remainingLessons');

    res.json({
      success: true,
      message: 'Lesson rescheduled successfully',
      data: {
        booking: updatedBooking,
        requiresApproval: newEvent.bookingInfo?.requiresApproval || false
      }
    });

  } catch (error) {
    console.error('Error rescheduling lesson:', error);
    createErrorResponse(res, 'Failed to reschedule lesson', 500);
  }
};
