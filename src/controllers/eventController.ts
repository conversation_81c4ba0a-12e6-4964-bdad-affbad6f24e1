import { Request, Response } from 'express';
import { Event } from '../models/Event';
import { Calendar } from '../models/calender';
// import mongoose from 'mongoose';

// Create event (only tutor who owns the calendar)
export const createEvent = async (req: Request, res: Response)  => {
  try {
    // Ensure only tutors can create events
    // if (req.user.role !== 'tutor') {
    //   return res.status(403).json({ message: 'Only tutors can create events' });
    // }

    const {
      calendarId,
      title,
      description,
      location,
      startDateTime,
      endDateTime,
      allDay,
      status,
      visibility,
      priority
    } = req.body;

    // Validate required fields
    if (!calendarId || !title || !startDateTime || !endDateTime) {
      return res.status(400).json({ message: 'calendarId, title, startDateTime, and endDateTime are required.' });
    }

    // if (!mongoose.Types.ObjectId.isValid(calendarId)) {
    //   return res.status(400).json({ message: 'Invalid calendarId' });
    // }

    // Check calendar existence and ownership
    const calendar = await Calendar.findById(calendarId);
    if (!calendar) {
      return res.status(404).json({ message: 'Calendar not found' });
    }

//    if (!calendar.ownerUserId || calendar.ownerUserId.toString() !== req.user.id) {
//   return res.status(403).json({ message: 'You do not own this calendar' });
// }


    // Create and save event
    const event = new Event({
      calendarId,
      title,
      description,
      location,
      startDateTime: new Date(startDateTime),
      endDateTime: new Date(endDateTime),
      allDay: allDay ?? false,
      // createdBy: req.user.id,
      status: status ?? 'confirmed',
      visibility: visibility ?? 'public',
      priority: priority ?? 3,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    await event.save();

    return res.status(201).json({ message: 'Event created successfully', event });
  } catch (error: any) {
    console.error('Create Event Error:', error);
    return res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};


// Get events by calendar (for learners or tutors)
export const getEventsByCalendar = async (req: Request, res: Response): Promise<any> => {
  try {
    const calendarId = req.params.calendarId;

    // Optionally, check if calendar is shared or owned by tutor
    const calendar = await Calendar.findById(calendarId);
    if (!calendar) return res.status(404).json({ message: 'Calendar not found' });

    // If user is learner, show only public events
    let query: any = { calendarId };
    if (req.user.role === 'learner') {
      query.visibility = 'public';
    }

    const events = await Event.find(query);
    res.json(events);
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};

// Update event (only tutor who created it)
export const updateEvent = async (req: Request, res: Response): Promise<any> => {
  try {
    if (req.user.role !== 'tutor') {
      return res.status(403).json({ message: 'Only tutors can update events' });
    }

    const eventId = req.params.id;
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: 'Event not found' });

    // if (event.createdBy.toString() !== req.user.id) {
    //   return res.status(403).json({ message: 'Not authorized to update this event' });
    // }

    const updates = req.body;
    Object.assign(event, updates, { updatedAt: new Date() });

    await event.save();
    res.json(event);
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};

// Delete event (only tutor who created it)
export const deleteEvent = async (req: Request, res: Response): Promise<any> => {
  try {
    if (req.user.role !== 'tutor') {
      return res.status(403).json({ message: 'Only tutors can delete events' });
    }

    const eventId = req.params.id;
    const event = await Event.findById(eventId);
    if (!event) return res.status(404).json({ message: 'Event not found' });

    // if (event.createdBy.toString() !== req.user.id) {
    //   return res.status(403).json({ message: 'Not authorized to delete this event' });
    // }

    await event.deleteOne();
    res.json({ message: 'Event deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};
