import { Request, Response } from 'express';
import { Calendar } from '../models/calender';

// Create a calendar (only tutor)
export const createCalendar = async (req: Request, res: Response): Promise<any> => {
  try {
    // if (req.user.role !== 'tutor') {
    //   return res.status(403).json({ message: 'Only tutors can create calendars' });
    // }

    const { name, description, color, isShared } = req.body;

    const calendar = new Calendar({
      // ownerUserId: req.user.id,
      name,
      description,
      color,
      isShared: isShared ?? true,
    });
    

    await calendar.save();
    res.status(201).json(calendar);
  } catch (error) {
    res.status(500).json({ message: 'Server error nothing happened' });
  }
};

// Get all calendars of a tutor (for learner or tutor)
export const getTutorCalendars = async (req: Request, res: Response) => {
  try {
    const tutorId = req.params.tutorId;

    // Optionally check if user has permission to view this tutor's calendar
    const calendars = await Calendar.find({ ownerUserId: tutorId, isShared: true });
    res.json(calendars);
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};

// Update calendar (only tutor who owns the calendar)
export const updateCalendar = async (req: Request, res: Response): Promise<any> => {
  try {
    const calendarId = req.params.id;
    const tutorId = req.user.id;

    const calendar = await Calendar.findById(calendarId);
    if (!calendar) return res.status(404).json({ message: 'Calendar not found' });
    if (calendar.ownerUserId.toString() !== tutorId) {
      return res.status(403).json({ message: 'Not authorized to update this calendar' });
    }

    const { name, description, color, isShared } = req.body;

    calendar.name = name ?? calendar.name;
    calendar.description = description ?? calendar.description;
    calendar.color = color ?? calendar.color;
    if (typeof isShared === 'boolean') calendar.isShared = isShared;
    calendar.updatedAt = new Date();

    await calendar.save();
    res.json(calendar);
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};

// Delete calendar (only tutor who owns the calendar)
export const deleteCalendar = async (req: Request, res: Response): Promise<any> => {
  try {
    const calendarId = req.params.id;
    const tutorId = req.user.id;

    const calendar = await Calendar.findById(calendarId);
    if (!calendar) return res.status(404).json({ message: 'Calendar not found' });
    if (calendar.ownerUserId.toString() !== tutorId) {
      return res.status(403).json({ message: 'Not authorized to delete this calendar' });
    }

    await calendar.deleteOne();
    
    res.json({ message: 'Calendar deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Server error' });
  }
};
