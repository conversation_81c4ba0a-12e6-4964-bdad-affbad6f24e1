import { Response } from 'express';
import { Types } from 'mongoose';
import { Calendar } from '../models/calender';
import { Event } from '../models/Event';
import { AuthRequest } from '../types/AuthRequest';
import { createErrorResponse } from '../middlewares/errorHandler';
import { checkBookingEligibility } from '../middlewares/subscriptionValidation';

// Create a calendar (only tutor)
export const createCalendar = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can create calendars', 403);
      return;
    }

    const {
      name,
      description,
      color,
      isShared,
      timezone,
      bookingSettings
    } = req.body;

    if (!name) {
      createErrorResponse(res, 'Calendar name is required', 400);
      return;
    }

    // Check if tutor already has a calendar with this name
    const existingCalendar = await Calendar.findOne({
      tutorId: req.user._id,
      name: name.trim()
    });

    if (existingCalendar) {
      createErrorResponse(res, 'You already have a calendar with this name', 409);
      return;
    }

    const calendar = new Calendar({
      tutorId: req.user._id,
      name: name.trim(),
      description,
      color: color || '#3B82F6',
      isShared: isShared ?? true,
      timezone: timezone || 'UTC',
      bookingSettings: {
        autoAcceptBookings: bookingSettings?.autoAcceptBookings ?? false,
        advanceBookingDays: bookingSettings?.advanceBookingDays ?? 30,
        minBookingNotice: bookingSettings?.minBookingNotice ?? 24,
        maxBookingsPerDay: bookingSettings?.maxBookingsPerDay ?? 8
      }
    });

    await calendar.save();

    const populatedCalendar = await Calendar.findById(calendar._id)
      .populate('tutorId', 'firstname lastname email');

    res.status(201).json({
      success: true,
      message: 'Calendar created successfully',
      data: populatedCalendar
    });

  } catch (error) {
    console.error('Error creating calendar:', error);
    createErrorResponse(res, 'Failed to create calendar', 500);
  }
};

// Get all calendars of a tutor (for learner or tutor)
export const getTutorCalendars = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    const tutorId = req.params.tutorId;

    if (!tutorId || !Types.ObjectId.isValid(tutorId)) {
      createErrorResponse(res, 'Invalid tutor ID format', 400);
      return;
    }

    // Build query based on user role and permissions
    let query: any = { tutorId: new Types.ObjectId(tutorId) };

    // If not the tutor themselves, only show shared calendars
    if (!req.user || (req.user._id as Types.ObjectId).toString() !== tutorId) {
      query.isShared = true;
      query.isActive = true;
    }

    const calendars = await Calendar.find(query)
      .populate('tutorId', 'firstname lastname email avatar')
      .sort({ createdAt: -1 });

    // If user is a student, add booking eligibility info
    let calendarsWithEligibility: any = calendars;
    if (req.user && req.user.role === 'student') {
      calendarsWithEligibility = await Promise.all(
        calendars.map(async (calendar) => {
          const eligibility = await checkBookingEligibility(
            req.user!._id as Types.ObjectId,
            calendar.tutorId._id
          );
          return {
            ...calendar.toObject(),
            bookingEligibility: eligibility
          };
        })
      );
    }

    res.json({
      success: true,
      data: calendarsWithEligibility
    });

  } catch (error) {
    console.error('Error fetching tutor calendars:', error);
    createErrorResponse(res, 'Failed to fetch calendars', 500);
  }
};

// Update calendar (only tutor who owns the calendar)
export const updateCalendar = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can update calendars', 403);
      return;
    }

    const calendarId = req.params.id;

    if (!calendarId || !Types.ObjectId.isValid(calendarId)) {
      createErrorResponse(res, 'Invalid calendar ID format', 400);
      return;
    }

    const calendar = await Calendar.findById(calendarId);
    if (!calendar) {
      createErrorResponse(res, 'Calendar not found', 404);
      return;
    }

    if (calendar.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'Not authorized to update this calendar', 403);
      return;
    }

    const {
      name,
      description,
      color,
      isShared,
      timezone,
      bookingSettings,
      isActive
    } = req.body;

    // Update fields
    if (name !== undefined) calendar.name = name.trim();
    if (description !== undefined) calendar.description = description;
    if (color !== undefined) calendar.color = color;
    if (isShared !== undefined) calendar.isShared = isShared;
    if (timezone !== undefined) calendar.timezone = timezone;
    if (isActive !== undefined) calendar.isActive = isActive;

    if (bookingSettings) {
      calendar.bookingSettings = {
        ...calendar.bookingSettings,
        ...bookingSettings
      };
    }

    await calendar.save();

    const updatedCalendar = await Calendar.findById(calendar._id)
      .populate('tutorId', 'firstname lastname email');

    res.json({
      success: true,
      message: 'Calendar updated successfully',
      data: updatedCalendar
    });

  } catch (error) {
    console.error('Error updating calendar:', error);
    createErrorResponse(res, 'Failed to update calendar', 500);
  }
};

// Delete calendar (only tutor who owns the calendar)
export const deleteCalendar = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can delete calendars', 403);
      return;
    }

    const calendarId = req.params.id;

    if (!calendarId || !Types.ObjectId.isValid(calendarId)) {
      createErrorResponse(res, 'Invalid calendar ID format', 400);
      return;
    }

    const calendar = await Calendar.findById(calendarId);
    if (!calendar) {
      createErrorResponse(res, 'Calendar not found', 404);
      return;
    }

    if (calendar.tutorId.toString() !== (req.user._id as Types.ObjectId).toString()) {
      createErrorResponse(res, 'Not authorized to delete this calendar', 403);
      return;
    }

    // Check if calendar has any future events
    const futureEvents = await Event.countDocuments({
      calendarId: calendar._id,
      startDateTime: { $gte: new Date() },
      status: { $in: ['available', 'booked'] }
    });

    if (futureEvents > 0) {
      createErrorResponse(res, 'Cannot delete calendar with future events. Please cancel or reschedule all future events first.', 400);
      return;
    }

    await calendar.deleteOne();

    res.json({
      success: true,
      message: 'Calendar deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting calendar:', error);
    createErrorResponse(res, 'Failed to delete calendar', 500);
  }
};

// Get tutor's own calendars
export const getMyCalendars = async (req: AuthRequest, res: Response): Promise<void> => {
  try {
    if (!req.user || req.user.role !== 'tutor') {
      createErrorResponse(res, 'Only tutors can view their calendars', 403);
      return;
    }

    const { includeInactive = false } = req.query;

    const query: any = { tutorId: req.user._id };
    if (!includeInactive) {
      query.isActive = true;
    }

    const calendars = await Calendar.find(query)
      .sort({ createdAt: -1 });

    // Get event counts for each calendar
    const calendarsWithStats = await Promise.all(
      calendars.map(async (calendar) => {
        const [totalEvents, upcomingEvents, bookedEvents] = await Promise.all([
          Event.countDocuments({ calendarId: calendar._id }),
          Event.countDocuments({
            calendarId: calendar._id,
            startDateTime: { $gte: new Date() }
          }),
          Event.countDocuments({
            calendarId: calendar._id,
            status: 'booked',
            startDateTime: { $gte: new Date() }
          })
        ]);

        return {
          ...calendar.toObject(),
          stats: {
            totalEvents,
            upcomingEvents,
            bookedEvents
          }
        };
      })
    );

    res.json({
      success: true,
      data: calendarsWithStats
    });

  } catch (error) {
    console.error('Error fetching tutor calendars:', error);
    createErrorResponse(res, 'Failed to fetch calendars', 500);
  }
};
