import { Router } from 'express';
import { createCalendar, getTutorCalendars, updateCalendar, deleteCalendar } from '../controllers/calendarController';
// import { authMiddleware } from '../middleware/authMiddleware';

const calender_router = Router();

// All routes protected, only tutors can create/update/delete
calender_router.post('/', createCalendar);
calender_router.get('/tutor/:tutorId',  getTutorCalendars);
calender_router.put('/:id', updateCalendar);
calender_router.delete('/:id', deleteCalendar);

export default calender_router;
